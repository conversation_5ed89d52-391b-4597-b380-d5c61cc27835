{"name": "rbac-server", "version": "1.0.0", "description": "RBAC Web App Backend", "main": "dist/server.js", "scripts": {"dev": "tsx watch src/server.ts", "build": "tsc", "start": "node dist/server.js", "type-check": "tsc --noEmit"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "jsonwebtoken": "^9.0.2", "bcryptjs": "^2.4.3", "dotenv": "^16.3.1"}, "devDependencies": {"@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/jsonwebtoken": "^9.0.5", "@types/bcryptjs": "^2.4.6", "@types/node": "^20.10.5", "typescript": "^5.3.3", "tsx": "^4.6.2"}}